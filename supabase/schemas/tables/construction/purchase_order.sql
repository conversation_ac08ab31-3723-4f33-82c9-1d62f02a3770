-- Purchase Order Table Schema
-- Contains purchase order information with project-level access control
-- Purchase orders are associated with projects and vendors
CREATE TABLE IF NOT EXISTS "public"."purchase_order" (
	"purchase_order_id" "uuid" DEFAULT "gen_random_uuid" () NOT NULL,
	"po_number" "text" NOT NULL,
	"description" "text",
	"po_date" "date" NOT NULL,
	"project_id" "uuid" NOT NULL,
	"vendor_id" "uuid" NOT NULL,
	"work_package_id" "uuid",
	"account" "text",
	"original_amount" numeric(15, 2),
	"co_amount" numeric(15, 2),
	"freight" numeric(15, 2),
	"tax" numeric(15, 2),
	"other" numeric(15, 2),
	"notes" "text",
	-- Standard audit fields
	"created_by_user_id" "uuid" DEFAULT "auth"."uid" () NOT NULL,
	"created_at" timestamp with time zone DEFAULT "timezone" ('utc'::"text", "now" ()) NOT NULL,
	"updated_at" timestamp with time zone DEFAULT "timezone" ('utc'::"text", "now" ()) NOT NULL
);

ALTER TABLE "public"."purchase_order" OWNER TO "postgres";

COMMENT ON TABLE "public"."purchase_order" IS 'Purchase orders for project procurement';

COMMENT ON COLUMN "public"."purchase_order"."po_number" IS 'Unique purchase order number';

COMMENT ON COLUMN "public"."purchase_order"."po_date" IS 'Date the purchase order was created';

COMMENT ON COLUMN "public"."purchase_order"."work_package_id" IS 'Associated work package for this purchase order';

COMMENT ON COLUMN "public"."purchase_order"."original_amount" IS 'Original purchase order amount';

COMMENT ON COLUMN "public"."purchase_order"."co_amount" IS 'Change order amount';

-- Primary key constraint
ALTER TABLE ONLY "public"."purchase_order"
ADD CONSTRAINT "purchase_order_pkey" PRIMARY KEY ("purchase_order_id");

-- Unique constraint for po_number within project
ALTER TABLE ONLY "public"."purchase_order"
ADD CONSTRAINT "purchase_order_po_number_project_unique" UNIQUE ("po_number", "project_id");

-- Foreign key constraints
ALTER TABLE ONLY "public"."purchase_order"
ADD CONSTRAINT "purchase_order_project_id_fkey" FOREIGN KEY ("project_id") REFERENCES "public"."project" ("project_id") ON UPDATE RESTRICT ON DELETE RESTRICT;

ALTER TABLE ONLY "public"."purchase_order"
ADD CONSTRAINT "purchase_order_vendor_id_fkey" FOREIGN KEY ("vendor_id") REFERENCES "public"."vendor" ("vendor_id") ON UPDATE RESTRICT ON DELETE RESTRICT;

ALTER TABLE ONLY "public"."purchase_order"
ADD CONSTRAINT "purchase_order_work_package_id_fkey" FOREIGN KEY ("work_package_id") REFERENCES "public"."work_package" ("work_package_id") ON UPDATE RESTRICT ON DELETE RESTRICT;

ALTER TABLE ONLY "public"."purchase_order"
ADD CONSTRAINT "purchase_order_created_by_user_id_fkey" FOREIGN KEY ("created_by_user_id") REFERENCES "public"."profile" ("user_id") ON UPDATE RESTRICT ON DELETE RESTRICT;

-- Indexes for performance
CREATE INDEX "purchase_order_project_id_idx" ON "public"."purchase_order" USING "btree" ("project_id");

CREATE INDEX "purchase_order_vendor_id_idx" ON "public"."purchase_order" USING "btree" ("vendor_id");

CREATE INDEX "purchase_order_work_package_id_idx" ON "public"."purchase_order" USING "btree" ("work_package_id");

CREATE INDEX "purchase_order_po_date_idx" ON "public"."purchase_order" USING "btree" ("po_date");

CREATE INDEX "purchase_order_created_by_user_id_idx" ON "public"."purchase_order" USING "btree" ("created_by_user_id");

-- Enable Row Level Security
ALTER TABLE "public"."purchase_order" ENABLE ROW LEVEL SECURITY;

-- Triggers for updated_at
CREATE OR REPLACE TRIGGER "update_updated_at" BEFORE
UPDATE ON "public"."purchase_order" FOR EACH ROW
EXECUTE FUNCTION "public"."update_updated_at_column" ();

-- Audit trigger for purchase_order table (function defined in shared/audit_functions.sql)
CREATE OR REPLACE TRIGGER "audit_purchase_order_trigger"
AFTER INSERT
OR
UPDATE
OR DELETE ON "public"."purchase_order" FOR EACH ROW
EXECUTE FUNCTION "public"."audit_purchase_order_changes" ();

-- Row Level Security Policies
-- SELECT policies - users can view purchase orders for projects they have access to
CREATE POLICY "Users can view purchase orders for accessible projects" ON "public"."purchase_order" FOR
SELECT
	TO "authenticated" USING (
		"public"."current_user_has_entity_access" ('project'::"public"."entity_type", "project_id")
	);

-- INSERT policies - users can create purchase orders for projects they have editor access to
CREATE POLICY "Users can create purchase orders for projects they can edit" ON "public"."purchase_order" FOR INSERT TO "authenticated"
WITH
	CHECK (
		"public"."current_user_has_entity_role" (
			'project'::"public"."entity_type",
			"project_id",
			'editor'::"public"."membership_role"
		)
	);

-- UPDATE policies - users can update purchase orders for projects they have editor access to
CREATE POLICY "Users can update purchase orders for projects they can edit" ON "public"."purchase_order"
FOR UPDATE
	TO "authenticated" USING (
		"public"."current_user_has_entity_role" (
			'project'::"public"."entity_type",
			"project_id",
			'editor'::"public"."membership_role"
		)
	)
WITH
	CHECK (
		"public"."current_user_has_entity_role" (
			'project'::"public"."entity_type",
			"project_id",
			'editor'::"public"."membership_role"
		)
	);

-- DELETE policies - users can delete purchase orders for projects they have admin access to
CREATE POLICY "Users can delete purchase orders for projects they can admin" ON "public"."purchase_order" FOR DELETE TO "authenticated" USING (
	"public"."current_user_has_entity_role" (
		'project'::"public"."entity_type",
		"project_id",
		'admin'::"public"."membership_role"
	)
);

-- Table-specific functions
-- Function to get purchase orders accessible to a user for a specific project
CREATE OR REPLACE FUNCTION "public"."get_accessible_purchase_orders" ("project_id_param" "uuid") RETURNS TABLE (
	"purchase_order_id" "uuid",
	"po_number" "text",
	"description" "text",
	"po_date" "date",
	"vendor_name" "text",
	"vendor_id" "uuid",
	"original_amount" numeric(15, 2),
	"co_amount" numeric(15, 2),
	"total_amount" numeric(15, 2),
	"created_at" timestamp with time zone,
	"created_by_name" "text"
) LANGUAGE "plpgsql" SECURITY DEFINER
SET
	"search_path" TO '' AS $$
DECLARE
	v_user_id UUID;
BEGIN
	-- Get the current user ID
	v_user_id := auth.uid();

	-- Check if user has access to the project
	IF NOT public.current_user_has_entity_access('project'::public.entity_type, project_id_param) THEN
		RETURN;
	END IF;

	-- Return purchase orders for the project with vendor and creator information
	RETURN QUERY
	SELECT
		po.purchase_order_id,
		po.po_number,
		po.description,
		po.po_date,
		v.name AS vendor_name,
		po.vendor_id,
		po.original_amount,
		po.co_amount,
		COALESCE(po.original_amount, 0) + COALESCE(po.co_amount, 0) + COALESCE(po.freight, 0) + COALESCE(po.tax, 0) + COALESCE(po.other, 0) AS total_amount,
		po.created_at,
		p.full_name AS created_by_name
	FROM public.purchase_order po
	LEFT JOIN public.vendor v ON po.vendor_id = v.vendor_id
	LEFT JOIN public.profile p ON po.created_by_user_id = p.user_id
	WHERE po.project_id = project_id_param
	ORDER BY po.po_date DESC, po.po_number;
END;
$$;

ALTER FUNCTION "public"."get_accessible_purchase_orders" ("project_id_param" "uuid") OWNER TO "postgres";

COMMENT ON FUNCTION "public"."get_accessible_purchase_orders" ("project_id_param" "uuid") IS 'Returns purchase orders accessible to the current user for a specific project';
